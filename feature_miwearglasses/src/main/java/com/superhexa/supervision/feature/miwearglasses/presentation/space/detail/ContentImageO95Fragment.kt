@file:Suppress("TooManyFunctions")

package com.superhexa.supervision.feature.miwearglasses.presentation.space.detail

import android.annotation.SuppressLint
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.superhexa.supervision.feature.miwearglasses.R
import com.superhexa.supervision.feature.miwearglasses.databinding.ItemViewpagerMediaDetailO95Binding
import com.superhexa.supervision.feature.miwearglasses.presentation.space.detail.MediaDetailO95FragmentViewModel.Companion.Stop
import com.superhexa.supervision.feature.miwearglasses.presentation.space.events.O95DownloadProgressEvent
import com.superhexa.supervision.feature.miwearglasses.presentation.space.events.O95NetDisconnectEvent
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.CompressUtil
import com.superhexa.supervision.library.base.basecommon.tools.GlideUtils
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.base.presentation.viewmodel.getViewModel
import com.superhexa.supervision.library.base.subscaleview.ImageSource
import com.superhexa.supervision.library.db.bean.MediaBean
import com.superhexa.supervision.library.statistic.O95Statistic
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber

/**
 * 类描述:文件控件详情页面MediaDetailFragment的ViewPager中的图片详情
 * 创建日期:2021/7/26 on 10:23 下午
 * 作者: FengPeng
 */
class ContentImageO95Fragment : InjectionFragment(R.layout.item_viewpager_media_detail_o95) {
    private val viewBinding: ItemViewpagerMediaDetailO95Binding by viewBinding()
    private val viewModel by lazy {
        getViewModel(requireActivity(), MediaDetailO95FragmentViewModel::class.java)
    }
    private var bean: MediaBean? = null
    private var position: Int = InvalidIndex
    private var enterTime = 0L

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)

        enterTime = System.currentTimeMillis()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        EventBus.getDefault().unregister(this)
    }

    override fun needDefaultbackground() = false

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        launch {
            arguments?.let { bundle ->
                position = bundle.getInt(BundleKey.Position, ContentVideoO95Fragment.InvalidIndex)
                if (position != ContentVideoO95Fragment.InvalidIndex && positionAvailable(position)) {
                    bean = viewModel.mutableList.value!![position]
                    bean?.let { configViewStatus(it) }
                }
            }
        }
        initListener()
    }

    private fun positionAvailable(position: Int): Boolean {
        return viewModel.mutableList.value != null && position < viewModel.mutableList.value!!.size
    }

    private fun initListener() {
        viewBinding.tiv.clickDebounce(viewLifecycleOwner) {
            if (isLandScope()) {
                viewModel.barFold.value = !viewModel.barFold.value!!
            }
        }
    }

    /**
     * 根据bean状态设置view可见性
     * @param bean MediaBean
     */
    private fun configViewStatus(bean: MediaBean) {
        when {
            bean.downloadState == MediaBean.Complete && bean.path != null -> {
                viewBinding.preview.llPreview.visibleOrgone(false)
                viewBinding.include.llError.visibleOrgone(false)
                val degree = CompressUtil.getPictureDegree(bean.path!!)
                Timber.e(
                    "degree $degree viewBinding.tiv.orientation ${viewBinding.tiv.orientation}"
                )
                viewBinding.tiv.setImage(ImageSource.uri(bean.path!!))
                viewBinding.tiv.orientation = degree
            }

            bean.downloadState == MediaBean.Downloading ||
                bean.downloadState == MediaBean.Wait -> {
                viewBinding.include.llError.visibleOrgone(false)
                viewBinding.preview.llPreview.visibleOrgone(true)
                GlideUtils.loadUrl(
                    requireContext(),
                    bean.thumbnailUrl,
                    viewBinding.preview.ivPreview
                )
                viewBinding.preview.progressBar.setProgress(bean.downloadProgress)
            }

            bean.downloadState == MediaBean.Error -> {
                viewBinding.preview.llPreview.visibleOrgone(false)
                viewBinding.include.llError.visibleOrgone(true)
            }
        }
    }

    private var isInOnResume = false

    override fun onResume() {
        super.onResume()
        isInOnResume = true
        GSYVideoManager.instance().releaseMediaPlayer()
        // 外层播放栏不显示播放 暂停
        viewModel.playLiveData.postValue(VideoState(Stop, ""))
        viewModel.muteToggleState.postValue(viewModel.muteToggleState.value!!.copy(state = Stop))
    }

    override fun onStop() {
        super.onStop()
        O95Statistic.cpaEvent(
            "Photo_Video_Manage_Time",
            tip = "1676.0.0.0.43045",
            hasDurationSec = true,
            durationSec = enterTime
        )
    }

    override fun onPause() {
        super.onPause()
        isInOnResume = false
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        bean?.takeIf { it.downloadState == MediaBean.Complete }?.path?.let { path ->
            val degree = CompressUtil.getPictureDegree(path)
            viewBinding.tiv.setImage(ImageSource.uri(path))
            viewBinding.tiv.orientation = degree
        }
    }

    @SuppressLint("SourceLockedOrientationActivity")
    @Suppress("EmptyFunctionBlock")
    override fun rotateConfig() {
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: O95DownloadProgressEvent) {
        bean?.let {
            if (it.identifier == event.identifier) {
                it.downloadProgress = event.downloadProgress
                it.downloadState = event.downloadState
                it.contentUri = event.contentUri
                it.path = event.path
                configViewStatus(it)
                // 如果刚完成下载，通知父Fragment更新按钮状态
                if (it.downloadState == MediaBean.Complete) {
                    viewModel.avaiableControllbar.postValue(false) // true 表示按钮可用（蒙版隐藏）
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: O95NetDisconnectEvent) {
        bean?.let {
            if (it.identifier == event.bean.identifier) {
                configViewStatus(event.bean)
            } else {
                // 重置下载状态不是完成的为异常，以便显示撕裂图片
                if (it.downloadState != MediaBean.Complete) {
                    it.downloadState = MediaBean.Error
                    configViewStatus(it)
                }
            }
        }
    }

    companion object {
        fun newInstance(position: Int): ContentImageO95Fragment {
            val args = Bundle()
            args.putInt(BundleKey.Position, position)
            val fragment = ContentImageO95Fragment()
            fragment.arguments = args
            return fragment
        }

        const val InvalidIndex = -1
        const val Completed = 100
    }
}
