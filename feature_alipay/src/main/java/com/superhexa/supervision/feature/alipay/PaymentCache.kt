@file:Suppress("MagicNumber", "VariableNaming")

package com.superhexa.supervision.feature.alipay

import timber.log.Timber

class PaymentCache {
    /**
     * 采样率16000Hz，单声道（channelCount=1），样本格式是16位PCM。
     * 计算方法是：采样率 × 样本大小 × 通道数 × 时间。样本大小是16位，也就是2字节。
     * 所以16000 * 2 * 1 * 20 = 640,000字节，也就是640KB。所以缓存上限应该是640KB。
     */
    private val firstVoiceBuffer = PcmBuffer(1024 * 1024)
    private val pendingVoiceBuffer = mutableListOf<ByteArray>()
    private var isPaymentIntent = false
    var isFirstVoiceSent = false
    var cacheFirstVoice = false

    fun cacheFirstVoiceData(data: ByteArray) {
        Timber.d("cacheFirstVoiceData $cacheFirstVoice")
        if (!cacheFirstVoice) return
        firstVoiceBuffer.addData(data)
    }

    fun getFirstVoiceCache(startTime: Long): List<ByteArray> = firstVoiceBuffer.getAudioDataFromTime(startTime)

    fun confirmPaymentIntent(reason: String) {
        Timber.d("confirmPaymentIntent:$reason")
        isPaymentIntent = true
        cacheFirstVoice = false
        // 重置首段语音发送状态
        isFirstVoiceSent = false
    }

    fun getIsPaymentIntent(): Boolean {
        Timber.d("getIsPaymentIntent $isPaymentIntent")
        return isPaymentIntent
    }

    // 新增：标记首段语音已发送完
    fun markFirstVoiceSent() {
        Timber.d("markFirstVoiceSent")
        isFirstVoiceSent = true
    }

    fun startFirstVoiceCache() {
        Timber.d("startFirstVoiceCache")
        if (isPaymentIntent) {
            Timber.d("Already in the payment process, no longer processing")
            return
        }
        cacheFirstVoice = true
    }

    /**
     * 缓存后续语音数据（新增：根据状态选择存储位置）
     */
    fun cacheVoiceData(data: ByteArray) {
        // 首段语音未发送完时，存入待发送缓冲区
        if (!isFirstVoiceSent) {
            pendingVoiceBuffer.add(data)
        }
    }

    // 新增：获取待发送缓冲区的数据
    fun takePendingVoiceData(): List<ByteArray> {
        Timber.d("takePendingVoiceData")
        val data = pendingVoiceBuffer.toList()
        pendingVoiceBuffer.clear()
        return data
    }

    fun clearFirstVoiceCache(reason: String) {
        Timber.d("clearFirstVoiceCache:$reason")
        firstVoiceBuffer.reset()
        cacheFirstVoice = false
    }

    fun clear(reason: String) {
        Timber.d("clear:$reason")
        firstVoiceBuffer.reset()
        pendingVoiceBuffer.clear()
        isFirstVoiceSent = false
        cacheFirstVoice = false
        isPaymentIntent = false
    }
}
