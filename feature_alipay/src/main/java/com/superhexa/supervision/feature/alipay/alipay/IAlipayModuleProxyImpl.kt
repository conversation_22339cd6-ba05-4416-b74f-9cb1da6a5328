package com.superhexa.supervision.feature.alipay.alipay

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.alibaba.android.arouter.facade.annotation.Route
import com.superhexa.supervision.feature.alipay.AlipaySDKManager
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.INHIBITION_ALIPAY
import com.superhexa.supervision.library.base.superhexainterfaces.alipay.IAlipayModuleProxy
import timber.log.Timber

@Route(path = INHIBITION_ALIPAY)
class IAlipayModuleProxyImpl : IAlipayModuleProxy {
    override fun initAlipay(context: Context, lifecycleOwner: LifecycleOwner?, sn: String) {
        Timber.d("initAlipay")
        AlipaySDKManager.INSTANCE.initialize(context, lifecycleOwner, sn = sn)
    }

    override fun releaseAlipay() {
        Timber.d("releaseAlipay")
        AlipaySDKManager.INSTANCE.close()
    }

    override suspend fun getBindStatus(): Boolean {
        Timber.d("getBindStatus")
        return AlipaySDKManager.INSTANCE.getBindStatus()
    }
}
