package com.superhexa.supervision.library.base.superhexainterfaces.alipay

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.superhexa.supervision.library.base.basecommon.arouter.IModuleApi

interface IAlipayModuleProxy : IModuleApi {
    fun initAlipay(context: Context, lifecycleOwner: LifecycleOwner? = null, sn: String)
    fun releaseAlipay()
    suspend fun getBindStatus(): <PERSON><PERSON><PERSON>
}
