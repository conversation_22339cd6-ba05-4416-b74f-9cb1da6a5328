package com.xiaomi.aivs.engine.event

import com.xiaomi.ai.api.Application
import com.xiaomi.ai.api.General
import com.xiaomi.ai.api.MultiModal
import com.xiaomi.ai.api.Nlp
import com.xiaomi.ai.api.Settings
import com.xiaomi.ai.api.SpeechRecognizer
import com.xiaomi.ai.api.SpeechSynthesizer
import com.xiaomi.ai.api.common.APIUtils
import com.xiaomi.ai.api.common.Context
import com.xiaomi.ai.api.common.Event
import com.xiaomi.ai.api.common.EventPayload
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.data.KeyWorld
import com.xiaomi.aivs.engine.context.RequestContextHolder
import com.xiaomi.aivs.engine.helper.ToneHelper
import com.xiaomi.aivs.engine.state.EngineStateMachine
import com.xiaomi.aivs.monitor.MonitorId

class EventBuilder(private val contextHolder: RequestContextHolder) {

    fun buildEvent(
        payload: EventPayload,
        params: Map<String, String>? = null,
        requestId: String? = null,
        withContext: Boolean = true
    ): Event<*> {
        assemblyEventPayload(payload, params)
        return if (withContext) {
            requestId?.let {
                APIUtils.buildEvent(payload, contextList(payload, params), requestId)
            } ?: run {
                APIUtils.buildEvent(payload, contextList(payload, params))
            }
        } else {
            requestId?.let {
                APIUtils.buildEvent(payload, listOf<Context<*>>(), requestId)
            } ?: run {
                APIUtils.buildEvent(payload)
            }
        }
    }

    /**
     * 创建跨设备指令所需的event
     */
    fun buildCrossDeviceEvent(
        payload: Application.CrossDeviceEvent,
        params: Map<String, String>?
    ): Event<*> {
        return APIUtils.buildEvent(
            payload,
            contextList(payload, params)
        )
    }

    @Suppress("ComplexMethod")
    private fun contextList(
        payload: EventPayload,
        params: Map<String, String>? = null
    ): List<Context<*>?> {
        val contextListResult = when (payload) {
            is General.ContextUpdate -> {
                when (params?.get(KeyWorld.MONITOR_ID)) {
                    MonitorId.STANDBY -> listOf(contextHolder.standbyContext())
                    MonitorId.MEDIA ->
                        listOf(contextHolder.createPlayStateContext(AiSpeechEngine.INSTANCE.appContext))

                    MonitorId.WEARABLE -> listOf(contextHolder.wearableSupportContext())
                    MonitorId.LOCATION -> contextHolder.devicePositionContext()
                    else -> contextHolder.createContextList(AiSpeechEngine.INSTANCE.appContext)
                }
            }

            is SpeechRecognizer.DuplexRecognizeStarted -> {
                val contextList = mutableListOf<Context<*>>()
                contextList.addAll(contextHolder.createContextList(AiSpeechEngine.INSTANCE.appContext))
                contextList.add(contextHolder.continuousDialogContext())
                contextList.add(contextHolder.requestStateContext())

                // 连续对话中,如果再次唤醒小爱,不清除Session.
                if (!EngineStateMachine.isContinuousDialog()) {
                    // 客户端主动请求清空session.
                    // - 背景: 在3.0接入协议下，服务端为每个接入的客户端维护一个统一的session，来缓存一些客户端的状态信息。
                    // - 触发: 当客户端决定清空服务器缓存的session时， 在上报SpeechRecognizer接口中的Recognize Event时带上该Context，
                    // 服务器接收到之后会清空对应的session.
                    contextList.add(APIUtils.buildContext(General.RenewSession()))
                }

                contextList
            }

            is MultiModal.ImageStreamStarted -> {
                val contextList = mutableListOf<Context<*>>()
                //    contextList.addAll(contextHolder.createContextList(AiSpeechEngine.INSTANCE.appContext))
                contextList.add(contextHolder.ttsConfigContext(createTtsConfig()))
                contextList
            }

            is MultiModal.ImageUnderstand -> {
                val contextList = mutableListOf<Context<*>>()
                // contextList.addAll(contextHolder.createContextList(AiSpeechEngine.INSTANCE.appContext))
                contextList.add(contextHolder.ttsConfigContext(createTtsConfig()))
                contextList.add(contextHolder.multiModalStateContext(params))
                contextList
            }

            is SpeechSynthesizer.Synthesize -> {
                val contextList = mutableListOf<Context<*>>()
                contextHolder.superXAiContext()?.let { contextList.add(it) }
                contextList
            }

            else -> {
                contextHolder.createContextList(AiSpeechEngine.INSTANCE.appContext)
            }
        }
        return contextListResult.toMutableList().apply {
            add(contextHolder.createRequestControlContext())
        }
    }

    private fun assemblyEventPayload(payload: EventPayload, params: Map<String, String>? = null) {
        when (payload) {
            is Nlp.Request -> {
                payload.setTts(ToneHelper.createTtsConfig(params))
            }

            is SpeechRecognizer.Recognize -> {
                payload.setTts(ToneHelper.createTtsConfig(params))
            }

            is SpeechRecognizer.DuplexRecognizeStarted -> {
                payload.setTts(createTtsConfig(params))
            }

            is SpeechSynthesizer.Synthesize -> {
                payload.setTts(ToneHelper.createTtsConfig(params))
            }
        }
    }

    private fun createTtsConfig(params: Map<String, String>? = null): Settings.TtsConfig {
        val config = ToneHelper.createTtsConfig(params)
        config.setAudioType(Settings.TtsAudioType.STREAM)
        // config.setStreamingAudioType(Settings.TtsAudioType.URL_WS)
        return config
    }
}
