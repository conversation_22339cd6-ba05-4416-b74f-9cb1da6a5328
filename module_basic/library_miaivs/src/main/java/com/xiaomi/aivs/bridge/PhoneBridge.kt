package com.xiaomi.aivs.bridge

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import android.os.Messenger
import com.fasterxml.jackson.databind.JsonNode
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.data.config.BuildConfig
import com.xiaomi.ai.api.Application
import com.xiaomi.ai.api.Application.CrossDeviceEvent
import com.xiaomi.ai.api.Execution
import com.xiaomi.ai.api.common.APIUtils
import com.xiaomi.ai.api.common.Instruction
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.config.ConfigCache
import com.xiaomi.aivs.data.KeyWorld
import com.xiaomi.aivs.engine.context.RequestContextHolder
import com.xiaomi.aivs.engine.event.EventBuilder
import com.xiaomi.aivs.engine.listener.ISpeechChatListener
import com.xiaomi.aivs.track.EventTrack
import com.xiaomi.aivs.utils.SpeechEngineHelper
import com.xiaomi.voiceassist.bridge.DeviceCategory
import com.xiaomi.voiceassist.bridge.MessageCategory
import com.xiaomi.voiceassist.bridge.contentprovider.DeviceLink
import com.xiaomi.voiceassist.bridge.contentprovider.SignedBridge
import com.xiaomi.voiceassist.bridge.utils.VersionUtils
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.launch
import kotlinx.coroutines.newSingleThreadContext
import timber.log.Timber

/**
 * 小米手机-小爱指令中转处理.
 * 文档:https://xiaomi.f.mioffice.cn/docx/doxk4nMfhrkkMZcISIyQ2C2h9zb
 * 多设备接入手机小爱:https://xiaomi.f.mioffice.cn/docx/doxk4M4RiTJreqMKwWvXs51QPkf
 */
class PhoneBridge : IBridge {

    // 由于概率出现非眼镜app的context传入导致调用sdk方法无权限, 这里直接使用applicationContext
    private val applicationContext = LibBaseApplication.instance.applicationContext
    private val bridgeProxy = SignedBridge()
    private val coroutineExceptionHandler = CoroutineExceptionHandler { _, exception ->
        Timber.d("$TAG Caught: $exception")
    }

    @OptIn(DelicateCoroutinesApi::class, ExperimentalCoroutinesApi::class)
    private val workScope =
        CoroutineScope(newSingleThreadContext(TAG) + coroutineExceptionHandler)
    private val handlerThread = HandlerThread("InstructionThread")
    private lateinit var messengerInstruction: Messenger

    private var ctx: String? = null
    private val glassCategory = DeviceCategory.GLASS
    private var currentDialogId: String? = null
    var config = Bundle().apply {
        putBoolean("is_dependence", true)
    }

    override fun init(context: Context) {
        Timber.tag(TAG).d(
            "init, glass app version -> " +
                "${applicationContext.packageManager.getPackageInfo(
                    applicationContext.packageName,
                    0
                ).versionName}"
        )
        if (VersionUtils.isDeviceLinkFramework(applicationContext)) {
            Timber.tag(TAG).i("init, support DeviceLinkFramework")
            if (!handlerThread.isAlive) {
                handlerThread.start()
            }
            messengerInstruction = Messenger(object : Handler(handlerThread.looper) {
                @Suppress("LongMethod")
                override fun handleMessage(msg: Message) {
                    super.handleMessage(msg)
                    val msgType = msg.what
                    Timber.tag(TAG).d("sendInstruction async callback $msg")
                    when (msgType) {
                        MessageCategory.INFO_EVENT.category -> {
                            trackCrossDeviceControlEvent(ActivityType.RECEIVED_EVENT_REQUEST)
                            val event = msg.data?.getString("event")
                            event?.let {
                                runCatching {
                                    val jsonNode = APIUtils.fromJsonString(it, JsonNode::class.java)
                                    val crossDeviceEventJsonNode =
                                        APIUtils.fromJsonNode(jsonNode, CrossDeviceEvent::class.java)
                                    val crossDeviceEvent =
                                        if (ConfigCache.rawEnvDomain() == SpeechEngineHelper.ENV_PREVIEW_TO_RELEASE) {
                                            // ptr环境
                                            val contextHolder = RequestContextHolder(PhoneBridge())
                                            val eventBuilder = EventBuilder(contextHolder)
                                            val params =
                                                mapOf(KeyWorld.REQUEST_ID to "$currentDialogId")
                                            eventBuilder.buildCrossDeviceEvent(
                                                crossDeviceEventJsonNode,
                                                params
                                            )
                                        } else {
                                            APIUtils.buildEvent(
                                                crossDeviceEventJsonNode,
                                                contextList(applicationContext)
                                            )
                                        }
                                    AiSpeechEngine.INSTANCE.postEvent(crossDeviceEvent)
                                }.onFailure { e ->
                                    Timber.tag(TAG).e("EventParsing failed, e -> ${e.message}")
                                }
                            }
                            Timber.tag(TAG).i("INFO_EVENT, value -> $event")
                        }
                        MessageCategory.INFO_TTS.category -> {
                            trackCrossDeviceControlEvent(ActivityType.RECEIVED_TTS_MESSAGE)
                            val tts = msg.data?.getString("tts")
                            val isNeedMicOpen = msg.data?.getBoolean("mic") // 播放tts完成之后是否开mic
                            val mMessengerCur = msg.replyTo
                            val dependenceType = msg.data?.getInt("dependenceType")
                            tts?.let {
                                val listener = object : ISpeechChatListener {
                                    override fun onQueryRecognize(
                                        sessionId: String?,
                                        dialogId: String?,
                                        query: String?,
                                        isFinal: Boolean,
                                        isFromPostImageForLinkImgId: Boolean?,
                                        instructionJson: String?,
                                        streamId: String?
                                    ) = Unit

                                    override fun onTextResponseSynthesizer(
                                        sessionId: String?,
                                        dialogId: String?,
                                        result: String?,
                                        isFinal: Boolean,
                                        instructionJson: String?,
                                        streamId: String?
                                    ) = Unit

                                    @Suppress("EmptyFunctionBlock")
                                    override fun onAlipayTextResponseSynthesizer(
                                        sessionId: String?,
                                        dialogId: String?,
                                        result: String?,
                                        isFinal: Boolean,
                                        instructionJson: String?,
                                        streamId: String?
                                    ) {}

                                    @Suppress("EmptyFunctionBlock")
                                    override fun addToChatHistory(
                                        sessionId: String?,
                                        dialogId: String,
                                        content: String,
                                        type: Int,
                                        timestamp: Long
                                    ) {}

                                    override fun onUtteranceDone(
                                        utteranceId: String?,
                                        isUrl: Boolean,
                                        isLocalCorpus: Boolean
                                    ) {
                                        Timber.tag(TAG).d("onUtteranceDone")
                                        AiSpeechEngine.INSTANCE.removeChatDataObserver(TAG)
                                        // 给手机小爱发送回执消息
                                        Timber.tag(TAG)
                                            .d("dependenceType: $dependenceType mMessengerCur: $mMessengerCur")
                                        if (dependenceType != null && mMessengerCur != null) {
                                            val message = Message.obtain()
                                            message.what = dependenceType
                                            mMessengerCur.send(message)
                                            Timber.tag(TAG)
                                                .d("sendMessage dependenceType: $dependenceType")
                                        }
                                        if (isNeedMicOpen == true) {
                                            val instruction = msg.data?.getString("instruction")
                                            Timber.tag(TAG).d("open mic instruction -> $instruction")
                                            instruction?.let {
                                                trackCrossDeviceControlEvent(
                                                    ActivityType.FORWARDING_EXECUTION_INSTRUCTIONS
                                                )
                                                Timber.tag(TAG).d("call sendInstruction open mic")
                                                val bundle = DeviceLink.sendInstruction(
                                                    context = applicationContext,
                                                    instruction = instruction,
                                                    windowState = WINDOW_STATE,
                                                    iMessenger = messengerInstruction,
                                                    category = glassCategory,
                                                    isTracer = VersionUtils.isDeviceLinkTracer(
                                                        applicationContext
                                                    ) && !BuildConfig.DEBUG,
                                                    config = config
                                                ).also {
                                                    trackCrossDeviceControlEvent(
                                                        ActivityType.RECEIVED_RETURN_SIGNAL
                                                    )
                                                }
                                                val keyCode = bundle?.getInt(KeyWorld.CODE)
                                                val executeSuccess = handleRetCode(keyCode)
                                                Timber.tag(TAG).d(
                                                    "sendInstruction called " +
                                                        "$keyCode,$instruction,$executeSuccess"
                                                )
                                            }
                                        }
                                    }
                                }
                                AiSpeechEngine.INSTANCE.addChatDataObserver(
                                    key = TAG,
                                    listener = listener
                                )
                                val params = mapOf(KeyWorld.REQUEST_ID to "$currentDialogId")
                                AiSpeechEngine.INSTANCE.startTts(it, params)
                                trackCrossDeviceControlEvent(ActivityType.SEND_TTS_REQUEST_TO_CLOUD)
                            }
                            Timber.tag(TAG).i("INFO_TTS, value -> $tts, isNeedMicOpen -> $isNeedMicOpen")
                        }
                        MessageCategory.INFO_CONTEXT.category -> {
                            ctx = msg.data?.getString("context")
                            Timber.tag(TAG).d("INFO_CONTEXT, value -> $ctx")
                        }
                        MessageCategory.INFO_INSTRUCTION.category -> {
                            trackCrossDeviceControlEvent(ActivityType.RECEIVED_RESULT_INSTRUCTION)
                            val status = msg.data?.getInt(KeyWorld.CODE)
                            val instruction = msg.data?.getString(KeyWorld.MESSAGE)
                            Timber.tag(TAG).d("INFO_INSTRUCTION, value -> $instruction, status -> $status")
                        }
                    }
                }
            })
        } else {
            Timber.tag(TAG).i("not need to init, not support DeviceLinkFramework")
        }
    }

    override fun sendInstruction(
        context: Context,
        dialogId: String?,
        instruction: Instruction<*>?
    ) {
        currentDialogId = dialogId
        trackCrossDeviceControlEvent(ActivityType.RECEIVED_COLLABORATIVE_INSTRUCTION)
        if (VersionUtils.isDeviceLinkFramework(applicationContext)) {
            workScope.launch {
                Timber.tag(TAG).i("call sendInstruction")
                val instructionStr = instruction?.let {
                    when (it.payload) {
                        is Execution.CrossDeviceControl -> {
                            APIUtils.toJsonString(
                                (it.payload as Execution.CrossDeviceControl).instructions
                            )
                        }

                        is Execution.CrossDeviceControlPhone -> {
                            APIUtils.toJsonString(
                                (it.payload as Execution.CrossDeviceControlPhone).instructions
                            )
                        }

                        else -> ""
                    }
                } ?: ""
                trackCrossDeviceControlEvent(ActivityType.FORWARDING_EXECUTION_INSTRUCTIONS)
                val bundle = DeviceLink.sendInstruction(
                    context = applicationContext,
                    instruction = instructionStr,
                    windowState = WINDOW_STATE,
                    iMessenger = messengerInstruction,
                    category = glassCategory,
                    isTracer = VersionUtils.isDeviceLinkTracer(
                        applicationContext
                    ) && !BuildConfig.DEBUG,
                    config = config
                ).also {
                    trackCrossDeviceControlEvent(ActivityType.RECEIVED_RETURN_SIGNAL)
                }
                val keyCode = bundle?.getInt(KeyWorld.CODE)
                val executeSuccess = handleRetCode(keyCode)
                Timber.tag(TAG).i("sendInstruction called $keyCode,$instructionStr,$executeSuccess")
            }
        } else {
            val params = mapOf(KeyWorld.REQUEST_ID to "$dialogId")
            AiSpeechEngine.INSTANCE.startTts("当前手机小爱版本过低，请升级到最新版本后重试", params)
        }
    }

    override fun contextList(context: Context): List<com.xiaomi.ai.api.common.Context<Application.CrossDeviceInfo>>? {
        return if (VersionUtils.isDeviceLinkFramework(applicationContext)) {
            runCatching {
                val typeToken = object :
                    TypeToken<List<com.xiaomi.ai.api.common.Context<Application.CrossDeviceInfo>>>() {}.type
                ctx?.let {
                    Timber.tag(TAG).d("contextData:$it")
                    val contextList: List<com.xiaomi.ai.api.common.Context<Application.CrossDeviceInfo>> =
                        Gson().fromJson(it, typeToken)
                    Timber.tag(TAG).d("contextList:${contextList.size}")
                    contextList
                } ?: run {
                    val bundle = DeviceLink.getContext(
                        context = applicationContext,
                        iMessenger = messengerInstruction,
                        category = glassCategory
                    )
                    val code = bundle?.getInt(KeyWorld.CODE) ?: ErrorCode.VERSION_LOW
                    val json = if (handleRetCode(code)) {
                        bundle?.getString(KeyWorld.REQUEST_CONTEXT)
                    } else {
                        ""
                    }
                    Timber.tag(TAG).d("contextData:$json")
                    val contextList: List<com.xiaomi.ai.api.common.Context<Application.CrossDeviceInfo>> =
                        Gson().fromJson(json, typeToken)
                    Timber.tag(TAG).d("contextList:${contextList.size}")
                    contextList
                }
            }.getOrNull()
        } else {
            runCatching {
                val bundle = bridgeProxy.getContext(applicationContext)
                val code = bundle?.getInt(KeyWorld.CODE) ?: ErrorCode.VERSION_LOW
                val json = if (handleRetCode(code)) {
                    bundle?.getString(KeyWorld.REQUEST_CONTEXT)
                } else {
                    ""
                }
                Timber.tag(TAG).d("contextData:$json")
                val typeToken = object :
                    TypeToken<List<com.xiaomi.ai.api.common.Context<Application.CrossDeviceInfo>>>() {}.type
                val contextList: List<com.xiaomi.ai.api.common.Context<Application.CrossDeviceInfo>> =
                    Gson().fromJson(json, typeToken)
                Timber.tag(TAG).d("contextList:${contextList.size}")
                contextList
            }.getOrNull()
        }
    }

    override fun release(context: Context) {
        Timber.tag(TAG).d("release")
        ctx = null
        currentDialogId = null
    }

    private fun handleRetCode(code: Int?): Boolean {
        Timber.tag(TAG).i("handleRetCode:$code")
        when (code) {
            ErrorCode.SUCCESS -> {
                return true
            }

            ErrorCode.EXECUTE_FAIL -> {
                Timber.tag(TAG).w("指令执行失败.")
            }

            ErrorCode.EXECUTE_CANCEL -> {
                Timber.tag(TAG).w("指令执行被取消")
            }

            ErrorCode.VERSION_LOW -> {
                Timber.tag(TAG).w("小爱版本低，暂不支持")
            }

            ErrorCode.EXECUTE_INSTRUCTION -> {
                Timber.tag(TAG).w("无权限或instruction解析失败")
            }

            ErrorCode.EXECUTE_CTA -> {
                Timber.tag(TAG).w("小爱未过CTA")
            }

            ErrorCode.EXECUTE_PERMISSION -> {
                Timber.tag(TAG).w("无转发权限")
            }

            ErrorCode.EXECUTE_TIMEOUT -> {
                Timber.tag(TAG).w("指令执行超时")
            }

            else -> {
                Timber.tag(TAG).w("小爱版本低，暂不支持")
            }
        }
        return false
    }

    private fun trackCrossDeviceControlEvent(activityType: String) {
        // 统一先判断手机小爱是否支持埋点，不支持就都不埋
        // 眼镜app是debug包就传false，避免产生脏数据（眼镜非签名包打点记录不上平台）
        val isDeviceLinkTracer = VersionUtils.isDeviceLinkTracer(applicationContext)
        Timber.tag(TAG).i(
            "isDeviceLinkTracer -> $isDeviceLinkTracer" +
                ", isDebug -> ${BuildConfig.DEBUG}"
        )
        if (!isDeviceLinkTracer || BuildConfig.DEBUG) return
        EventTrack.trackCrossDeviceControlEvent(
            activityType = activityType,
            traceId = currentDialogId
        )
    }

    companion object {
        private const val TAG = "PhoneBridge"

        // 0：悬浮态 1：全屏态
        private const val WINDOW_STATE = 0
    }
}

private object ErrorCode {
    // 指令执行成功.
    const val SUCCESS = 0

    // 指令执行失败.
    const val EXECUTE_FAIL = 2

    // 指令执行被取消.
    const val EXECUTE_CANCEL = 1

    // 小爱版本过低.
    const val VERSION_LOW = -1

    // 无权限或instruction解析失败.
    const val EXECUTE_INSTRUCTION = -2

    // 小爱未过CTA.
    const val EXECUTE_CTA = -3

    // 无转发权限.
    const val EXECUTE_PERMISSION = -4

    // 指令执行超时.
    const val EXECUTE_TIMEOUT = -5
}

// 协同响应埋点事件类型
private object ActivityType {
    // 眼镜收到协同响应的指令
    const val RECEIVED_COLLABORATIVE_INSTRUCTION = "received_collaborative_instruction"

    // 眼镜将执行指令传给手机
    const val FORWARDING_EXECUTION_INSTRUCTIONS = "forwarding_execution_instructions"

    // 眼镜将执行指令传给手机，收到手机返回的信号
    const val RECEIVED_RETURN_SIGNAL = "received_return_signal"

    // 眼镜收到手机发送的tts信息
    const val RECEIVED_TTS_MESSAGE = "received_tts_message"

    // 眼镜向云端发送tts请求
    const val SEND_TTS_REQUEST_TO_CLOUD = "send_tts_request_to_cloud"

    // 眼镜收到手机发送的event请求
    const val RECEIVED_EVENT_REQUEST = "received_event_request"

    // 眼镜收到手机发送的指令执行结果
    const val RECEIVED_RESULT_INSTRUCTION = "received_result_instruction"
}
