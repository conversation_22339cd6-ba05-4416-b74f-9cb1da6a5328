package com.superhexa.supervision.feature.xiaoai.service

import android.content.Context
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.lifecycleScope
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.superhexa.music.MusicApiService
import com.superhexa.music.data.ErrorCode
import com.superhexa.music.data.MusicCp
import com.superhexa.music.data.RadioStationCp
import com.superhexa.supervision.feature.xiaoai.R
import com.superhexa.supervision.feature.xiaoai.data.Config
import com.superhexa.supervision.feature.xiaoai.glass.WearableImpl
import com.superhexa.supervision.feature.xiaoai.presentation.chat.ChatHistoryDbHelper
import com.superhexa.supervision.feature.xiaoai.presentation.observer.LocationPolicy
import com.superhexa.supervision.feature.xiaoai.track.EventTrackHelper
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.config.Constants
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.media.MediaVolumeMonitor
import com.superhexa.supervision.library.base.basecommon.network.NetworkMonitor
import com.superhexa.supervision.library.base.superhexainterfaces.alipay.IAlipayModuleProxy
import com.superhexa.supervision.library.base.superhexainterfaces.miwear.IMiWearModuleApi
import com.superhexa.supervision.library.db.bean.ChatRecord
import com.superhexa.supervision.library.db.bean.MessageType
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import com.xiaomi.ai.api.Template
import com.xiaomi.ai.api.common.Instruction
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.data.DialogState
import com.xiaomi.aivs.data.StreamType
import com.xiaomi.aivs.data.model.AccountConfig
import com.xiaomi.aivs.data.model.AuthConfig
import com.xiaomi.aivs.engine.event.DeviceEvent
import com.xiaomi.aivs.engine.helper.MusicSource
import com.xiaomi.aivs.engine.listener.ISpeechChatListener
import com.xiaomi.aivs.engine.listener.ISpeechEngineListener
import com.xiaomi.aivs.engine.proxy.SpeechEngineProxyImpl
import com.xiaomi.aivs.engine.state.EngineStateMachine
import com.xiaomi.aivs.monitor.MonitorId
import com.xiaomi.wearable.context
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.Collections

/**
 * 小爱Lite组件.
 */
object MiLiteComponent {

    private const val OBSERVER_NAME = "MiLite"
    private val chatHistoryDbHelper = ChatHistoryDbHelper()
    private var bondDevice: BondDevice? = null

    private val networkListener: (Boolean, Boolean) -> Unit = { isConnected, isValid ->
        Timber.d("onNetworkState:$isConnected,$isValid")
        if (isValid) {
            EngineStateMachine.onConnected()
        } else {
            EngineStateMachine.onDisconnected()
        }
    }

    private val volumeChangeListener: (Int) -> Unit = { volume ->
        Timber.d("volumeChangeListener:$volume")
        AiSpeechEngine.INSTANCE.onMonitorEvent(MonitorId.MEDIA)
    }

    fun init(context: Context, lifecycleOwner: LifecycleOwner) {
        Timber.d("init")
        AiSpeechEngine.INSTANCE.appContext = context.applicationContext
        AiSpeechEngine.INSTANCE.addEngineObserver(
            key = OBSERVER_NAME,
            lifecycle = lifecycleOwner.lifecycle,
            listener = engineObserver
        )
        AiSpeechEngine.INSTANCE.addChatDataObserver(
            key = OBSERVER_NAME,
            lifecycle = lifecycleOwner.lifecycle,
            listener = chatObserver
        )
        // 小爱SDK添加扩展.
        AiSpeechEngine.INSTANCE.addGlassFunc(WearableImpl(dbHelper = chatHistoryDbHelper))
    }

    fun startUp(context: Context, newDevice: BondDevice, lifecycleOwner: LifecycleOwner) {
        Timber.d("startUp:$bondDevice,$newDevice")
        newDevice.takeIf { it.mac != bondDevice?.mac }?.let {
            this.bondDevice = newDevice
            addFuncMonitor(context, lifecycleOwner)
            initAiSpeech(newDevice)
            initAlipay(lifecycleOwner, newDevice.sn ?: "")
        }
    }

    private fun initAlipay(lifecycleOwner: LifecycleOwner, sn: String) {
        Timber.d("initAlipay:$sn")
        IAlipayModuleProxy::class.java.impl.initAlipay(
            LibBaseApplication.instance,
            lifecycleOwner,
            sn
        )
    }

    private fun initAiSpeech(bondDevice: BondDevice) {
        Timber.d("initAiSpeech:${bondDevice.mac}")
        val authConfig = AuthConfig(
            clientId = Config.Auth.DEVICE_OAUTH_CLIENT_ID,
            clientSecret = Config.Auth.DEVICE_OAUTH_CLIENT_SECRET,
            redirectUrl = Config.Auth.DEVICE_OAUTH_REDIRECT_URL,
            platformId = Config.PLATFORM_ID,
            deviceId = "${bondDevice.deviceId}"
        )
        AiSpeechEngine.INSTANCE.releaseEngine()
        AiSpeechEngine.INSTANCE.init(context, authConfig)
        AiSpeechEngine.INSTANCE.startup(
            accountConfig = AccountConfig(
                AccountManager.getUserID(),
                AccountManager.getOauthToken(),
                Constants.inStaging()
            )
        )
    }

    private fun addFuncMonitor(context: Context, lifecycleOwner: LifecycleOwner) {
        MusicApiService.INSTANCE.init(
            context,
            onPlayInfoChange = {
                AiSpeechEngine.INSTANCE.onMonitorEvent(MonitorId.MEDIA)
            },
            onPlayState = {
                AiSpeechEngine.INSTANCE.onMonitorEvent(MonitorId.MEDIA)
            },
            onError = { cp, code ->
                val cpName = when (cp) {
                    MusicCp.QQ -> context.getString(R.string.cp_qq_music)
                    MusicCp.NET_EASE -> context.getString(R.string.cp_netease_music)
                    RadioStationCp.XMLY -> context.getString(R.string.cp_xmly_music)
                    else -> context.getString(R.string.cp_qq_music)
                }
                handMusicCode(context, cpName, code)
            }
        )
        NetworkMonitor.addNetworkStateListener(lifecycleOwner.lifecycle, networkListener)
        MediaVolumeMonitor.addVolumeChangeListener(
            lifecycleOwner.lifecycle,
            volumeChangeListener
        )
    }

    fun release(withObserver: Boolean = true) {
        bondDevice?.let {
            Timber.d("release:$withObserver")
            if (withObserver) {
                AiSpeechEngine.INSTANCE.destroy()
            } else {
                AiSpeechEngine.INSTANCE.releaseEngine()
            }
            IAlipayModuleProxy::class.java.impl.releaseAlipay()
            MusicApiService.INSTANCE.release()
            NetworkMonitor.removeNetworkStateListener(networkListener)
            MediaVolumeMonitor.removeVolumeChangeListener(volumeChangeListener)
            bondDevice = null
        }
    }

    fun addMainActivityLifecycleObserver(
        lifecycleOwner: LifecycleOwner,
        isPageLoad: LiveData<Boolean>
    ) {
        lifecycleOwner.lifecycle.addObserver(object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                Timber.d("MainActivityLifecycle onStateChanged:$event")
                when (event) {
                    // 检查standby是否从后台回到前台.
                    Lifecycle.Event.ON_RESUME -> {
                        if (isPageLoad.value == true &&
                            AiSpeechEngine.INSTANCE.dialogState() == DialogState.STANDBY
                        ) {
                            Timber.d("launchStandbyActivity ON_RESUME.")
                            MiLiteHelper.launchStandbyActivity(LibBaseApplication.instance)
                        }
                    }

                    Lifecycle.Event.ON_DESTROY -> {
                        lifecycleOwner.lifecycle.removeObserver(this)
                    }

                    else -> {}
                }
            }
        })

        lifecycleOwner.lifecycleScope.launch {
            isPageLoad.observe(lifecycleOwner) {
                val currentState = lifecycleOwner.lifecycle.currentState
                Timber.d("isPageLoad:${isPageLoad.value}")
                if (isPageLoad.value == true &&
                    currentState == Lifecycle.State.RESUMED &&
                    AiSpeechEngine.INSTANCE.dialogState() == DialogState.STANDBY
                ) {
                    Timber.d("launchStandbyActivity onPageLoad.")
                    MiLiteHelper.launchStandbyActivity(LibBaseApplication.instance)
                }
            }
        }
    }

    private val engineObserver = object : ISpeechEngineListener {

        override fun onConnectState(isConnected: Boolean) {
            Timber.d("onConnectState:$isConnected")
        }

        override fun onDialogState(state: Int) {
            Timber.d("onDialogState:$state")
            when (state) {
                DialogState.RECORDING -> {
                    MiLiteHelper.onDialogEnter(LibBaseApplication.instance)
                    LocationPolicy.startObserver(LibBaseApplication.instance)
                }

                DialogState.VOICE_IDLE -> {
                    LocationPolicy.stopObserver()
                    MiLiteHelper.onDialogExit(LibBaseApplication.instance)
                }

                else -> {}
            }
        }

        override fun sendEventToDevice(
            transactionId: String?,
            requestId: String?,
            payload: String?,
            event: Int
        ) {
            Timber.d("sendEventToDevice:$transactionId,$requestId,$event")
            kotlin.runCatching {
                when (event) {
                    DeviceEvent.VOICE_STOP_CAPTURE -> {
                        IMiWearModuleApi::class.java.impl.sendAivsInstruction()
                    }

                    DeviceEvent.IMAGE_CAPTURE_AND_TRANS -> {
                        AiSpeechEngine.INSTANCE.stopTts(
                            dialogId = "IMAGE_CAPTURE_AND_TRANS",
                            stopOptions = SpeechEngineProxyImpl.Companion.TtsStopOptions(
                                calledFrom = "IMAGE_CAPTURE_AND_TRANS",
                                needResumeMediaPlayer = null,
                                needStopMediaPlayer = null,
                                stopReason = null
                            )
                        )
                        transactionId?.takeIf { requestId != null }?.let {
                            IMiWearModuleApi::class.java.impl.requestAivsMultiModal(
                                transactionId = transactionId,
                                requestId = requestId!!,
                                payload,
                                cmd = IMiWearModuleApi.CMD_START_IMAGE_TYP
                            )
                        }
                    }

                    DeviceEvent.IMAGE_TRANS_STOP -> {
                        transactionId?.takeIf { requestId != null }?.let {
                            IMiWearModuleApi::class.java.impl.requestAivsMultiModal(
                                transactionId = transactionId,
                                requestId = requestId!!,
                                payload,
                                cmd = IMiWearModuleApi.CMD_STOP_IMAGE_TYP
                            )
                        }
                    }

                    DeviceEvent.LARGE_IMAGE_TRANS_START -> {
                        transactionId?.takeIf { requestId != null }?.let {
                            IMiWearModuleApi::class.java.impl.requestAivsMultiModal(
                                transactionId = transactionId,
                                requestId = requestId!!,
                                payload,
                                cmd = IMiWearModuleApi.CMD_START_RECORD_TYP
                            )
                        }
                    }

                    DeviceEvent.LARGE_IMAGE_TRANS_STOP -> {
                        transactionId?.takeIf { requestId != null }?.let {
                            IMiWearModuleApi::class.java.impl.requestAivsMultiModal(
                                transactionId = transactionId,
                                requestId = requestId!!,
                                payload,
                                cmd = IMiWearModuleApi.CMD_STOP_RECORD_TYP
                            )
                        }
                    }

                    else -> {}
                }
            }.getOrElse {
                it.printStackTrace()
            }
        }

        override fun sendEventToDevice(transactionId: String?, requestId: String?, event: Int) {
            sendEventToDevice(transactionId, requestId, null, event)
        }

        override fun syncStateToDevice(dialogState: Int, engineState: Int, ttsState: Int) {
            Timber.d("syncStateToDevice:$dialogState,$engineState,$ttsState")
            kotlin.runCatching {
                IMiWearModuleApi::class.java.impl.syncAivsStatus(dialogState, engineState, ttsState)
            }.getOrElse { it.printStackTrace() }
        }

        override fun onSpeechEventTrack(
            eventName: String,
            params: Map<String, Any>?
        ) {
            // 创建 params 的不可变副本用于日志（避免并发修改）
            val logParams = params?.let { Collections.unmodifiableMap(HashMap(it)) } ?: emptyMap()
            Timber.d("onSpeechEventTrack:$eventName,$logParams")
            params?.let {
                EventTrackHelper.doEventTrack(eventName, params)
            }
        }
    }

    private val chatObserver = object : ISpeechChatListener {
        override fun onQueryRecognize(
            sessionId: String?,
            dialogId: String?,
            query: String?,
            isFinal: Boolean,
            isFromPostImageForLinkImgId: Boolean?,
            instructionJson: String?,
            streamId: String?
        ) {
            Timber.d("onQueryRecognize:$sessionId,$dialogId,$query,$isFinal,$streamId,$instructionJson")
            if (!isFinal) return
            val validDialogId = dialogId ?: return
            val validQuery = query?.takeIf { it.isNotEmpty() } ?: return
            MainScope().launch {
                chatHistoryDbHelper.putChatRecordWithLock(validDialogId, sessionId) { record ->
                    // 1. 更新基础字段
                    if (false == isFromPostImageForLinkImgId) {
                        record.query = validQuery
                    }
                    record.streamId = streamId

                    // 2. 处理指令列表
                    instructionJson?.let { json ->
                        val timestampedJson = addTimeStampToJsonString(json)
                        record.instructionList?.add(timestampedJson)
                    }
                }
            }
        }

        override fun onImageQAContent(
            dialogId: String?,
            sessionId: String?,
            imgInstruction: Instruction<*>?
        ) {
            Timber.d("onImageQAContent:$sessionId,$dialogId,$sessionId,$imgInstruction")
            val validDialogId = dialogId ?: return
            MainScope().launch {
                chatHistoryDbHelper.putChatRecordWithLock(validDialogId, sessionId) { record ->
                    imgInstruction?.payload?.let { payload ->
                        record.imageId?.let {
                            (payload as? Template.ImageQAContent)?.setHoldImgIds(
                                arrayListOf(it)
                            )
                        }
                    }

                    imgInstruction?.let { img ->
                        val timestampedJson = addTimeStampToJsonString(img.toString())
                        record.instructionList?.add(timestampedJson)
                    }
                }
            }
        }

        override fun onImageQuery(dialogId: String?, requestId: String, instructionJson: String?) {
            Timber.d("onImageQuery:$dialogId,$requestId")
            MainScope().launch {
                dialogId?.let {
                    chatHistoryDbHelper.updateImageResponse(it, requestId, instructionJson)
                }
            }
        }

        override fun onTextResponseSynthesizer(
            sessionId: String?,
            dialogId: String?,
            result: String?,
            isFinal: Boolean,
            instructionJson: String?,
            streamId: String?
        ) {
            Timber.d("onTextResponseSynthesizer:$dialogId,$result,$isFinal,$streamId,$instructionJson")
            if (!isFinal) return

            val validDialogId = dialogId ?: return
            val validResult = result ?: return
            MainScope().launch {
                chatHistoryDbHelper.putChatRecordWithLock(validDialogId, sessionId) { record ->
                    record.response = validResult

                    instructionJson?.let { json ->
                        val timestampedJson = addTimeStampToJsonString(json.toString())
                        record.instructionList?.add(timestampedJson)
                    }
                }
            }
        }

        override fun onAlipayTextResponseSynthesizer(
            sessionId: String?,
            dialogId: String?,
            result: String?,
            isFinal: Boolean,
            instructionJson: String?,
            streamId: String?
        ) {
            Timber.d("onAlipayTextResponseSynthesizer:$dialogId,$result,$isFinal,$streamId,$instructionJson")
            if (!isFinal) return

            val validDialogId = dialogId ?: return
            val validResult = result ?: return
            MainScope().launch {
                chatHistoryDbHelper.putChatRecordWithLock(validDialogId, sessionId) { record ->
                    record.response = validResult
                    record.streamId = streamId
                    instructionJson?.let { json ->
                        val timestampedJson = addTimeStampToJsonString(json.toString())
                        record.instructionList?.add(timestampedJson)
                    }
                }
            }
        }

        override fun onResponseBottomExplain(
            sessionId: String?,
            dialogId: String?,
            bottomExplain: String?,
            instructionJson: String?
        ) {
            Timber.d("onResponseBottomExplain:$dialogId,$bottomExplain,$instructionJson")
            val validDialogId = dialogId ?: return
            val validBottomExplain = bottomExplain?.takeIf { it.isNotEmpty() } ?: return
            MainScope().launch {
                chatHistoryDbHelper.putChatRecordWithLock(
                    validDialogId,
                    sessionId
                ) { record ->
                    record.bottomExplain = validBottomExplain

                    instructionJson?.let { json ->
                        val timestampedJson = addTimeStampToJsonString(json.toString())
                        record.instructionList?.add(timestampedJson)
                    }
                }
            }
        }

        override fun onParkingCard(
            dialogId: String?,
            sessionId: String?,
            title: String,
            subTitle: String,
            url: String,
            instructionJson: String?
        ) {
            Timber.d("onParkingCard：$dialogId, $sessionId,$instructionJson")
            val validDialogId = dialogId ?: return
            MainScope().launch {
                chatHistoryDbHelper.putChatRecordWithLock(
                    validDialogId,
                    sessionId
                ) { record ->
                    instructionJson.let { json ->
                        val timestampedJson = addTimeStampToJsonString(json.toString())
                        record.instructionList?.add(timestampedJson)
                    }
                }
            }
        }

        override fun onStreamDialogEnter(
            sessionId: String?,
            dialogId: String?,
            streamType: String,
            streamId: String,
            cardConfig: Triple<String, String, String>?,
            pageConfig: Triple<String, String, String?>?,
            instructionJson: String?
        ) {
            Timber.d("onStreamDialogEnter:$dialogId,$sessionId,$streamId,$cardConfig,$pageConfig")
            MainScope().launch {
                MiLiteHelper.onStreamEnter(
                    context = LibBaseApplication.instance,
                    notifyTitle = cardConfig?.first
                        ?: context.getString(R.string.title_standby),
                    pageTitle = pageConfig?.first ?: context.getString(R.string.title_standby),
                    icon = cardConfig?.third ?: "",
                    avatar = pageConfig?.second ?: "",
                    welcome = pageConfig?.third?.let { Pair(dialogId, it) }
                )
                sessionId?.takeIf { it.isNotEmpty() && dialogId.isNotNullOrEmpty() }?.let {
                    chatHistoryDbHelper.putChatRecord(
                        ChatRecord(
                            messageType = MessageType.STREAM_CARD,
                            sessionId = sessionId,
                            dialogId = dialogId!!,
                            title = cardConfig?.first
                                ?: context.getString(R.string.title_message_standby),
                            description = cardConfig?.second
                                ?: context.getString(R.string.tip_standby),
                            iconUrl = if (streamType != StreamType.STANDBY) cardConfig?.third else ""
                        ),
                        instructionJson
                    )
                }
            }
        }

        override fun onDialogIllegal(sessionId: String?, dialogId: String?) {
            Timber.d("onDialogIllegal")
            MainScope().launch {
                dialogId?.let {
                    val record = chatHistoryDbHelper.queryUserRecord(dialogId)
                    Timber.d("onDialogIllegal:record $record")
                    record.forEach {
                        chatHistoryDbHelper.deleteItem(it)
                    }
                }
            }
        }

        override fun addToChatHistory(
            sessionId: String?,
            dialogId: String,
            content: String,
            type: Int,
            timestamp: Long
        ) {
            Timber.d("addToChatHistory:$sessionId,$dialogId,$content,$type")
            // 每次存进去的ID都要不一致，不然web端会去重，导致不显示
            val instructionId = "mockId${System.currentTimeMillis()}"
            if (type == 0) {
                val toastInstruction =
                    AiSpeechEngine.INSTANCE.generateToastInstruction(content, instructionId, dialogId)
                onAlipayTextResponseSynthesizer(
                    sessionId,
                    dialogId,
                    dialogId,
                    true,
                    instructionJson = toastInstruction.toString()
                )
            } else {
                val queryInstruction =
                    AiSpeechEngine.INSTANCE.generateQueryInstruction(content, instructionId, dialogId)
                onQueryRecognize(
                    sessionId,
                    dialogId,
                    query = content,
                    isFinal = true,
                    false,
                    instructionJson = queryInstruction.toString()
                )
            }
        }
    }

    private fun addTimeStampToJsonString(instruction: String?): String {
        Timber.d("addTimeStampToJsonString:$instruction")
        if (instruction.isNullOrBlank() || instruction == "null") {
            Timber.w("Invalid instruction JSON: $instruction")
            return "{}" // 返回安全空对象
        }
        // 获取当前时间戳
        val timestamp = System.currentTimeMillis()
        // 将 instruction 转换为 JSON 字符串（假设 toString() 已经返回 JSON 格式）
        val instructionJsonString = instruction ?: return ""
        // 使用 Gson 解析 JSON 字符串
        val json = Gson().fromJson(instructionJsonString, JsonObject::class.java)
        // 确保 header 存在并为 JsonObject
        if (json.has("header") && json.get("header").isJsonObject) {
            val headerJson = json.getAsJsonObject("header")
            headerJson.addProperty("timestamp", timestamp)
        }
        return json.toString()
    }

    @Suppress("ComplexMethod")
    private fun handMusicCode(context: Context, cpName: String, @ErrorCode code: Int?) {
        Timber.d("handQMusicCode:$cpName,$code")
        val tips: String = when (code) {
            ErrorCode.NOT_INSTALL -> context.getString(
                R.string.tips_music_error_not_install,
                cpName
            )

            ErrorCode.NOT_AUTH -> {
                if (MusicSource.DEFAULT == AiSpeechEngine.INSTANCE.getMusicSource()) {
                    context.getString(R.string.tips_music_error_not_auth)
                } else {
                    context.getString(R.string.tips_music_error_app_not_auth, cpName)
                }
            }

            ErrorCode.NOT_LOGIN -> context.getString(R.string.tips_music_error_not_login, cpName)
            ErrorCode.NOT_PRIVACY -> context.getString(R.string.tips_music_error_privacy, cpName)
            ErrorCode.NO_SOURCE -> context.getString(R.string.tips_music_error_no_source)
            ErrorCode.NO_VIP -> context.getString(R.string.tips_music_error_vip)
            ErrorCode.NO_COPYRIGHT -> context.getString(R.string.tips_music_error_copyright)
            ErrorCode.NO_FAVORITES -> context.getString(R.string.tips_music_error_favorites)
            ErrorCode.BIND_FAIL -> context.getString(
                R.string.tips_music_error_bind_fail,
                cpName
            )
            ErrorCode.DATA_BLOCKED -> context.getString(
                R.string.tips_music_error_data_blocked,
                cpName
            )
            ErrorCode.PLAY_FAIL -> context.getString(
                R.string.tips_music_error_play_fail,
                cpName
            )
            ErrorCode.REQUEST_FAIL -> context.getString(
                R.string.tips_music_error_request_fail
            )
            ErrorCode.OPEN_PRIVATE_ROAMING_FAIL -> context.getString(
                R.string.tips_music_error_open_private_roaming_fail
            )

            else -> context.getString(R.string.tips_music_error_other, cpName)
        }
        if (tips.isNotEmpty()) {
            AiSpeechEngine.INSTANCE.startTts(tips)
        }
    }
}
